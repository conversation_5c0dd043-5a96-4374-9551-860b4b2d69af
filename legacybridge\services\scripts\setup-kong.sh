#!/bin/bash

# Configure Kong API Gateway for LegacyBridge services
set -e

KONG_ADMIN_URL="http://localhost:8001"

echo "🌉 Configuring Kong API Gateway..."

# Function to check if Kong is ready
wait_for_kong() {
    echo "⏳ Waiting for Kong Admin API..."
    until curl -f $KONG_ADMIN_URL/status > /dev/null 2>&1; do
        sleep 2
    done
    echo "✅ Kong Admin API is ready"
}

# Function to create service if it doesn't exist
create_service() {
    local service_name=$1
    local service_host=$2
    local service_port=$3
    local service_path=${4:-"/"}
    
    echo "📝 Creating service: $service_name"
    
    # Check if service exists
    if curl -s $KONG_ADMIN_URL/services/$service_name | grep -q "\"name\":\"$service_name\""; then
        echo "  ℹ️  Service $service_name already exists, updating..."
        curl -X PATCH $KONG_ADMIN_URL/services/$service_name \
            --data "protocol=http" \
            --data "host=$service_host" \
            --data "port=$service_port" \
            --data "path=$service_path" > /dev/null
    else
        curl -X POST $KONG_ADMIN_URL/services \
            --data "name=$service_name" \
            --data "protocol=http" \
            --data "host=$service_host" \
            --data "port=$service_port" \
            --data "path=$service_path" > /dev/null
    fi
    echo "  ✅ Service $service_name configured"
}

# Function to create route if it doesn't exist
create_route() {
    local service_name=$1
    local route_name=$2
    local route_path=$3
    
    echo "🛣️  Creating route: $route_name"
    
    # Check if route exists
    if curl -s $KONG_ADMIN_URL/routes/$route_name | grep -q "\"name\":\"$route_name\""; then
        echo "  ℹ️  Route $route_name already exists, updating..."
        curl -X PATCH $KONG_ADMIN_URL/routes/$route_name \
            --data "paths[]=$route_path" \
            --data "strip_path=true" > /dev/null
    else
        curl -X POST $KONG_ADMIN_URL/services/$service_name/routes \
            --data "name=$route_name" \
            --data "paths[]=$route_path" \
            --data "strip_path=true" > /dev/null
    fi
    echo "  ✅ Route $route_name configured"
}

# Function to add plugin if it doesn't exist
add_plugin() {
    local plugin_name=$1
    local service_name=$2
    shift 2
    local plugin_config=("$@")
    
    echo "🔌 Adding plugin: $plugin_name to $service_name"
    
    # Check if plugin exists for this service
    if curl -s "$KONG_ADMIN_URL/services/$service_name/plugins" | grep -q "\"name\":\"$plugin_name\""; then
        echo "  ℹ️  Plugin $plugin_name already exists for $service_name"
        return
    fi
    
    local curl_cmd="curl -X POST $KONG_ADMIN_URL/services/$service_name/plugins --data \"name=$plugin_name\""
    for config in "${plugin_config[@]}"; do
        curl_cmd="$curl_cmd --data \"$config\""
    done
    
    eval $curl_cmd > /dev/null
    echo "  ✅ Plugin $plugin_name added to $service_name"
}

# Function to add global plugin
add_global_plugin() {
    local plugin_name=$1
    shift
    local plugin_config=("$@")
    
    echo "🌐 Adding global plugin: $plugin_name"
    
    # Check if global plugin exists
    if curl -s "$KONG_ADMIN_URL/plugins" | grep -q "\"name\":\"$plugin_name\""; then
        echo "  ℹ️  Global plugin $plugin_name already exists"
        return
    fi
    
    local curl_cmd="curl -X POST $KONG_ADMIN_URL/plugins --data \"name=$plugin_name\""
    for config in "${plugin_config[@]}"; do
        curl_cmd="$curl_cmd --data \"$config\""
    done
    
    eval $curl_cmd > /dev/null
    echo "  ✅ Global plugin $plugin_name added"
}

# Wait for Kong to be ready
wait_for_kong

# Create services
create_service "auth-service" "host.docker.internal" "3001" "/api/v1"
create_service "conversion-service" "host.docker.internal" "3002" "/api/v1"
create_service "file-service" "host.docker.internal" "3003" "/api/v1"
create_service "job-service" "host.docker.internal" "3004" "/api/v1"

# Create routes
create_route "auth-service" "auth-route" "/auth"
create_route "conversion-service" "conversion-route" "/convert"
create_route "file-service" "file-route" "/files"
create_route "job-service" "job-route" "/jobs"

# Add global plugins
echo "🔧 Configuring global plugins..."

# CORS plugin
add_global_plugin "cors" \
    "config.origins=*" \
    "config.methods=GET,POST,PUT,DELETE,OPTIONS,PATCH" \
    "config.headers=Content-Type,Authorization,X-Requested-With" \
    "config.exposed_headers=X-Auth-Token" \
    "config.credentials=true" \
    "config.max_age=3600"

# Rate limiting plugin
add_global_plugin "rate-limiting" \
    "config.minute=100" \
    "config.policy=local" \
    "config.fault_tolerant=true" \
    "config.hide_client_headers=false"

# Request/Response logging
add_global_plugin "file-log" \
    "config.path=/tmp/access.log"

# Prometheus metrics
add_global_plugin "prometheus"

# Request size limiting
add_global_plugin "request-size-limiting" \
    "config.allowed_payload_size=100"

echo "🔐 Configuring service-specific plugins..."

# JWT authentication for protected services (not auth service)
for service in "conversion-service" "file-service" "job-service"; do
    add_plugin "jwt" "$service" \
        "config.secret_is_base64=false" \
        "config.key_claim_name=iss"
done

# Request transformer for auth service
add_plugin "request-transformer" "auth-service" \
    "config.add.headers=X-Service-Name:auth-service"

# Response transformer for all services
for service in "auth-service" "conversion-service" "file-service" "job-service"; do
    add_plugin "response-transformer" "$service" \
        "config.add.headers=X-Service-Name:$service" \
        "config.add.headers=X-Response-Time:\$(latency)"
done

echo "✅ Kong configuration completed!"
echo ""
echo "🔗 API Gateway endpoints:"
echo "  🔐 Authentication: http://localhost:8000/auth"
echo "  🔄 Conversion:     http://localhost:8000/convert"
echo "  📁 Files:          http://localhost:8000/files"
echo "  ⚙️  Jobs:           http://localhost:8000/jobs"
echo ""
echo "🛠️  Kong Admin:"
echo "  📊 Admin API:      http://localhost:8001"
echo "  📈 Metrics:        http://localhost:8001/metrics"
echo ""
echo "🎯 Next steps:"
echo "  1. Start the microservices"
echo "  2. Test the API endpoints"
echo "  3. Monitor metrics at http://localhost:9090"
