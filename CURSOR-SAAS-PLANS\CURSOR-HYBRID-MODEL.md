# CURSOR Option 2 – Hybrid Desktop + Cloud Plan (Recommended)

## 1. Executive Summary
Ship a **local-first** desktop application (Tauri) that works fully offline, then unlocks **premium cloud-powered features**—sync, collaboration, AI processing—through a subscription.  Best of both worlds: frictionless installation + recurring revenue.

---

## 2. Market Research & Opportunity
1. **Target personas**  
   • Pros: existing desktop users who now demand cloud sync  
   • SMBs with mixed online/offline workflows  
   • Compliance-heavy orgs needing self-hosted option
2. **Pain points solved**  
   • Data always accessible, but privacy is respected; choose what syncs.  
   • Pay only for added value.
3. **TAM/SAM/SOM**  
   • Assume 1 M desktop installs ⇒ 10–30 % convert to cloud tier at $8–$15/mo.

---

## 3. Product Tiers & Pricing
| Tier          | Price           | Key Features |
|---------------|-----------------|--------------|
| Free / Local  | $0 (one-time $79 if Option-1 purchased) | Full offline app |
| Plus          | $9.99/mo        | Cross-device sync, automatic backups |
| Pro           | $19.99/mo       | Real-time collaboration, AI add-ons |
| Enterprise    | Custom          | SSO, on-prem deployment, support SLA |

*Add mobile companion app (Flutter) as upsell in Year-2.*

---

## 4. System Architecture
```
            +--------------+                    +------------------+
            | Desktop App  |   HTTPS/WebSocket  |  Cloud Services   |
            |  (Tauri)     | ─────────────────▶ | (Kubernetes)      |
            +--------------+                    +------------------+
                    ▲                                   ▲
                    │                                   │
          Local SQlite / Files                   Postgres + S3
```
1. **Local**: App runs offline with SQLite.  
2. **Sync Engine**: CRDT-based delta sync via WebSocket when online.  
3. **Cloud**: Node.js (NestJS) REST + WS API, PostgreSQL, Redis, S3, deployed on **AWS EKS**.

### Key Components
| Component          | Tech                | Notes |
|--------------------|---------------------|-------|
| Desktop Frontend   | SvelteKit + Tauri   | 100 % code reuse from Option-1 |
| Sync Service       | Rust (Axum) or Go   | CRDT diffing, end-to-end encryption |
| API Gateway        | AWS API Gateway     | Rate limiting, API keys |
| Auth               | **Auth0** / Cognito | OIDC, SSO, social login |
| Payments           | **Stripe** Billing  | Subscriptions, coupon codes |
| Orchestration      | AWS EKS (K8s)       | Autoscaling |
| Data Storage       | RDS Postgres        | Multi-AZ, 15 read replicas |
| Caching            | ElastiCache Redis   | Session + doc delta cache |
| Object Storage     | S3 + CloudFront     | Attachments, backups |
| Observability      | Grafana + Loki      | Metrics, logs |
| Feature Flags      | LaunchDarkly        | Gradual rollout |
| Mobile API         | GraphQL             | Future mobile apps |

---

## 5. CI/CD & DevOps Workflow
1. **Mono-repo** (PNPM workspaces) – desktop & cloud code share libs.  
2. GitHub Actions → Unit tests → Docker build → Helm chart → Staging EKS.  
3. Canary release with feature flags; promote to prod after health checks.  
4. Desktop auto-update served by S3 (same as Option-1).

---

## 6. Data Synchronization Design
• Use **Yjs** (CRDT) library embedded in app; generate updates (Uint8 arrays).  
• Persist updates locally; when online push to `/sync` WS channel.  
• Server merges using Yjs & stores state in Postgres (JSONB).  
• Conflict-free, works with intermittent connectivity.

---

## 7. Subscription Handling Flow
```
App → Stripe Checkout → webhook /stripe/webhook →
 1. Validate signature
 2. Create/Update tenant record in Postgres
 3. Issue JWT subscription token
```
Desktop stores token in secure storage (Keychain/DPAPI). Token sent in `Authorization: Bearer` on API/WS calls.

---

## 8. Implementation Roadmap
| Phase | Duration | Milestones |
|-------|----------|------------|
| P0    | 3 wks    | Set up EKS cluster, CI pipeline |
| P1    | 4 wks    | Implement Sync & Auth microservices |
| P2    | 3 wks    | Integrate Stripe, subscription entitlements |
| P3    | 2 wks    | Beta with internal users; load testing (k6) |
| P4    | 4 wks    | Public beta, feature flag rollout |
| GA    | –        | Marketing launch, partner integrations |

---

## 9. Marketing Plan
1. **Freemium funnel**: Users start offline; subtle upsell banner for cloud sync.  
2. **Email drip** showing benefits of Plus tier after 7, 14, 30 days.  
3. **SEO**: Blog on "why local-first apps beat pure SaaS".  
4. Case studies from beta testers (publish on ProductHunt launch).  
5. Expansion loops: invite collaborator → both get Pro for 1 month.

---

## 10. KPIs & Metrics
| Metric                     | 6-mo Target |
|----------------------------|-------------|
| Cloud conversion rate      | ≥15 % |
| MRR                        | $30 k |
| Churn (logo)               | ≤3 % / mo |
| Weekly active subscribers  | ≥60 % |
| Uptime (SLA)               | ≥99.9 % |

---

## 11. Risks & Mitigations
| Risk                | Mitigation |
|---------------------|------------|
| Sync data loss      | CRDT + nightly S3 backups |
| Cloud cost overrun  | KEDA auto-scaling, per-doc compression |
| Compliance (GDPR)   | Data residency via AWS region selection |
| Feature creep       | Feature flags, roadmap discipline |

---

## 12. Next-Steps Checklist
- [ ] Spin up EKS via Terraform  
- [ ] Implement Yjs proof-of-concept sync  
- [ ] Configure Stripe test mode & webhooks  
- [ ] Design subscription entitlements schema  
- [ ] Draft ToS & DPA for cloud users  
- [ ] Seed content calendar for SEO  
