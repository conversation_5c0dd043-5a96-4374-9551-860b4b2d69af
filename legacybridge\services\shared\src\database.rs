// Database connection and management utilities
use crate::error::{ServiceError, ServiceResult};
use sqlx::{PgPool, Row};
use std::time::Duration;
use uuid::Uuid;

pub struct DatabaseManager {
    pool: PgPool,
}

impl DatabaseManager {
    pub async fn new(database_url: &str) -> ServiceResult<Self> {
        let pool = sqlx::postgres::PgPoolOptions::new()
            .max_connections(20)
            .min_connections(5)
            .acquire_timeout(Duration::from_secs(10))
            .idle_timeout(Duration::from_secs(600))
            .max_lifetime(Duration::from_secs(1800))
            .connect(database_url)
            .await?;

        // Run migrations
        sqlx::migrate!("./migrations").run(&pool).await?;

        Ok(Self { pool })
    }

    pub fn pool(&self) -> &PgPool {
        &self.pool
    }

    pub async fn health_check(&self) -> ServiceResult<()> {
        sqlx::query("SELECT 1")
            .fetch_one(&self.pool)
            .await?;
        Ok(())
    }

    pub async fn get_system_config(&self, key: &str) -> ServiceResult<Option<serde_json::Value>> {
        let row = sqlx::query("SELECT value FROM system_config WHERE key = $1")
            .bind(key)
            .fetch_optional(&self.pool)
            .await?;

        Ok(row.map(|r| r.get(0)))
    }

    pub async fn set_system_config(
        &self,
        key: &str,
        value: &serde_json::Value,
        updated_by: Uuid,
    ) -> ServiceResult<()> {
        sqlx::query(
            "INSERT INTO system_config (key, value, updated_by) 
             VALUES ($1, $2, $3) 
             ON CONFLICT (key) DO UPDATE SET 
             value = EXCLUDED.value, 
             updated_by = EXCLUDED.updated_by, 
             updated_at = NOW()"
        )
        .bind(key)
        .bind(value)
        .bind(updated_by)
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn record_performance_metric(
        &self,
        metric_name: &str,
        metric_value: f64,
        labels: Option<&serde_json::Value>,
    ) -> ServiceResult<()> {
        sqlx::query(
            "INSERT INTO performance_metrics (metric_name, metric_value, labels) 
             VALUES ($1, $2, $3)"
        )
        .bind(metric_name)
        .bind(metric_value)
        .bind(labels)
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn log_audit_event(
        &self,
        user_id: Option<Uuid>,
        event_type: &str,
        resource_type: &str,
        resource_id: Option<Uuid>,
        old_values: Option<&serde_json::Value>,
        new_values: Option<&serde_json::Value>,
        ip_address: Option<&str>,
        user_agent: Option<&str>,
    ) -> ServiceResult<()> {
        sqlx::query(
            "INSERT INTO audit_events (user_id, event_type, resource_type, resource_id, old_values, new_values, ip_address, user_agent)
             VALUES ($1, $2, $3, $4, $5, $6, $7, $8)"
        )
        .bind(user_id)
        .bind(event_type)
        .bind(resource_type)
        .bind(resource_id)
        .bind(old_values)
        .bind(new_values)
        .bind(ip_address.map(|ip| ip.parse::<std::net::IpAddr>().ok()).flatten())
        .bind(user_agent)
        .execute(&self.pool)
        .await?;

        Ok(())
    }
}

// Repository pattern for database operations
pub trait Repository<T> {
    type Id;
    type CreateRequest;
    type UpdateRequest;

    async fn create(&self, request: Self::CreateRequest) -> ServiceResult<T>;
    async fn find_by_id(&self, id: Self::Id) -> ServiceResult<Option<T>>;
    async fn update(&self, id: Self::Id, request: Self::UpdateRequest) -> ServiceResult<T>;
    async fn delete(&self, id: Self::Id) -> ServiceResult<()>;
    async fn list(&self, limit: i64, offset: i64) -> ServiceResult<Vec<T>>;
    async fn count(&self) -> ServiceResult<i64>;
}

// User repository
use crate::types::User;

#[derive(Debug, Clone, serde::Deserialize)]
pub struct CreateUserRequest {
    pub username: String,
    pub email: String,
    pub password: String,
    pub roles: Vec<String>,
}

#[derive(Debug, Clone, serde::Deserialize)]
pub struct UpdateUserRequest {
    pub username: Option<String>,
    pub email: Option<String>,
    pub roles: Option<Vec<String>>,
    pub is_active: Option<bool>,
}

pub struct UserRepository {
    pool: PgPool,
}

impl UserRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    pub async fn find_by_username(&self, username: &str) -> ServiceResult<Option<User>> {
        let user = sqlx::query_as!(
            User,
            r#"
            SELECT id, username, email, roles as "roles: Vec<String>", 
                   created_at, updated_at, last_login_at, is_active
            FROM users 
            WHERE username = $1 AND is_active = true
            "#,
            username
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(user)
    }

    pub async fn find_by_email(&self, email: &str) -> ServiceResult<Option<User>> {
        let user = sqlx::query_as!(
            User,
            r#"
            SELECT id, username, email, roles as "roles: Vec<String>", 
                   created_at, updated_at, last_login_at, is_active
            FROM users 
            WHERE email = $1 AND is_active = true
            "#,
            email
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(user)
    }

    pub async fn update_last_login(&self, user_id: Uuid) -> ServiceResult<()> {
        sqlx::query!(
            "UPDATE users SET last_login_at = NOW() WHERE id = $1",
            user_id
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_password_hash(&self, user_id: Uuid) -> ServiceResult<Option<String>> {
        let row = sqlx::query!(
            "SELECT password_hash FROM users WHERE id = $1 AND is_active = true",
            user_id
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(row.map(|r| r.password_hash))
    }
}

impl Repository<User> for UserRepository {
    type Id = Uuid;
    type CreateRequest = CreateUserRequest;
    type UpdateRequest = UpdateUserRequest;

    async fn create(&self, request: Self::CreateRequest) -> ServiceResult<User> {
        let user_id = Uuid::new_v4();
        let password_hash = crate::auth::password::hash_password(&request.password)?;

        let user = sqlx::query_as!(
            User,
            r#"
            INSERT INTO users (id, username, email, password_hash, roles)
            VALUES ($1, $2, $3, $4, $5)
            RETURNING id, username, email, roles as "roles: Vec<String>", 
                     created_at, updated_at, last_login_at, is_active
            "#,
            user_id,
            request.username,
            request.email,
            password_hash,
            &request.roles
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(user)
    }

    async fn find_by_id(&self, id: Self::Id) -> ServiceResult<Option<User>> {
        let user = sqlx::query_as!(
            User,
            r#"
            SELECT id, username, email, roles as "roles: Vec<String>", 
                   created_at, updated_at, last_login_at, is_active
            FROM users 
            WHERE id = $1 AND is_active = true
            "#,
            id
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(user)
    }

    async fn update(&self, id: Self::Id, request: Self::UpdateRequest) -> ServiceResult<User> {
        let user = sqlx::query_as!(
            User,
            r#"
            UPDATE users 
            SET username = COALESCE($2, username),
                email = COALESCE($3, email),
                roles = COALESCE($4, roles),
                is_active = COALESCE($5, is_active),
                updated_at = NOW()
            WHERE id = $1
            RETURNING id, username, email, roles as "roles: Vec<String>", 
                     created_at, updated_at, last_login_at, is_active
            "#,
            id,
            request.username,
            request.email,
            request.roles.as_ref().map(|r| r.as_slice()),
            request.is_active
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(user)
    }

    async fn delete(&self, id: Self::Id) -> ServiceResult<()> {
        sqlx::query!("UPDATE users SET is_active = false WHERE id = $1", id)
            .execute(&self.pool)
            .await?;

        Ok(())
    }

    async fn list(&self, limit: i64, offset: i64) -> ServiceResult<Vec<User>> {
        let users = sqlx::query_as!(
            User,
            r#"
            SELECT id, username, email, roles as "roles: Vec<String>", 
                   created_at, updated_at, last_login_at, is_active
            FROM users 
            WHERE is_active = true
            ORDER BY created_at DESC
            LIMIT $1 OFFSET $2
            "#,
            limit,
            offset
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(users)
    }

    async fn count(&self) -> ServiceResult<i64> {
        let count = sqlx::query!("SELECT COUNT(*) as count FROM users WHERE is_active = true")
            .fetch_one(&self.pool)
            .await?
            .count
            .unwrap_or(0);

        Ok(count)
    }
}
